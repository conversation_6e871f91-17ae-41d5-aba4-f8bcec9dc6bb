const pages = {
  home: {
    welcome: "Welcome",
    your_app: "Your Apps",
    other_app: "Explore Other Apps",
    download_mobile: "Download our software in your mobile!",
    find_madia: "Find us in social media!",
    screenList_item_0: "Compatible with All Operating System",
    screenList_item_1: "Cloud-bassed",
    screenList_item_2: "Can be Used for Digital Signages, LED and LCD Screens",

    nuTagList_item_1: "Can be Integrated with any POS System",
    nuTagList_item_2: "Cloud-based",
    nuTagList_item_3: "Scalable",
    nuTagList_item_4: "Real-time and Scheduled Updates",
    nuTagList_item_5: "Customizable Templates",

    zataList_item_1: "High Accuracy",
    zataList_item_2: "Cloud-based",
    zataList_item_3: "Real-Time Monitoring and Report",
    zataList_item_4: "AI-driven Optimization",
    zataList_item_5: "Privacy and Compliance",
  },
  appCenter: {
    title: "Application Center",
    cms_content:
      "Create, manage, and update screen content at anytime and from anywhere",
    nutag_content:
      "Remotely manage pricing, promotion, and various other content on Electronic Shelf Label",
    zata_content:
      "Advanced AI-powered data processing software to accurately identify gender, age, and attributes",
    open: "Open",
    subscribe: "Subscribe",
  },
  system_setting: {},
  branch: {
    name: "Name",
    region: "Branch Region",
    email: "Branch Owner Email",
    mobile: "Branch Owner Mobile",
    title: "Branch List",
    add_branch: "Add New Branch",
    edit_branch: "Edit Branch",
    delete_branch: "Delete Confirmation",
    enter_name: "Please Enter Branch Name",
    branch_email_required: "Please Enter Branch Email",
    delete_content:
      "Are you sure, you want to delete this branch <br /> employee permanently?",
    view_branch: "View Branch",
    branch_region: "Branch Region",
    branch_region_required: "Please Enter Region",
    branch_email: "Email Address",
    branch_mobile: "Mobile Number",
  },

  branch_user: {
    logo: "Company Logo",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email Address",
    phone: "Mobile Number",
    roleNames: "Authorization Level",
    enter_firstName: "Please Enter firstName",
    enter_lastName: "Please Enter LastName",

    enter_email: "Please Enter Email",
    add_title: "Add Branch Employee",
    edit_title: "Edit Branch Employee",
    auth_level: "Authorization Level *",
    view_branch: "View Branch Employee",
    region: "Region",
    owner_email: "Organization Owner Email",
    owner_mobile: "Organization Owner Mobile",
    address: "Address",
    area_name: "Area Name",
    title: "Branch Employee"
  },
  retail: {},
  parter: {},
  system_log: {},
  data_permission: {
    title: "Data Permission",
    name: "Data Permission Name",
    type: "Permission Type",
    add_title: "Add Data Permission",
    edit_title: "Edit Data Permission",
    enter_name: "Please Enter Data Permission Name",
    select_type: "Please select the permission level",
    add_permission: "Data Permission",
    this_level:
      "Whether to select this level and all levels below permissions?",
    email: "Email",
    select_outlets: "Select Outlets",
    sure_delete: "Are you sure, you want to delete this Data Permission?",
    devices_data:
      "Some devices and other data bound to this principal,<br /> can not be delete.",
    retail_client: "Retail Client Permissions",
    partner_permissions: "Partner Permissions",
    retail_client_info: "Retail Client Info",
    partner_info: "Partner Info",

  },

  datascope: {
    all_data: "All data under the account",
    custom_tip: "Customize partner permissions",
    custom_retail: "Customize retail client permissions",
    currently_highest: "Currently at the highest level of authority.",
    all_data_tip: "It includes all users and stores under the current account.",
    retail_client_name: "Retail Client Name",
    retail_client_account: "Retail Client Account",
    retail_client_name_placeholder: "Please Enter Retail Client Name",
    enter_outlet_name: "Please Enter Outlet Name",
    principals_selection: "Principals Selection",
    partner_selection: "Partner Company Selection",
    principals_selection_placeholder: "Search by Principal Name",
    partner_selection_placeholder: "Search by Partner Name",
    principals_list: "Principals List",
    partner_list: "Partner Company List",
    selected_principals: "Selected Principals",
    selected_partner_companies: "Selected Partner Companies",
    selected: "selected",
    outlet_selection: "Outlet Selection",
    selected_outlets: "Selected outlets"




  },
  area: {
    title: "Area",
    messageBox_title: "Delete Area",
    area_area_name: "Area Name",
    area_del: 'Are you sure you want to delete the "{{name}}" area?',
    ips_enter_region: "Please select an area",
    select_an_area: "Select an Area",
    accelerate: "Area Accelerate",
    region_center: "Region Center Point Location",
    superior_area: "Superior Area",
    enter_region_name: "Enter a Region Name",
    area_required: "Please select outlet location",
    new_area: "New Area",
    "operation.create": "Create Area",
    "operation.update": "Update Area",
    "operation.delete": "Delete Area",
  },
  roles: {
    title: "Authorization Level",
    name: " Authorization Level Name",
    department_name: "Department Name",
    department_level: "Department Level",
    enter_roles_name: "Please Enter Aunthorization Level Name",
    add_auth_level: "Add Authorization Level",
    edit_auth_level: "Edit Authorization Level",
    view_auth_level: "View Authorization Level",
    authorization_level_placeholder: "Please select Authorization Level",




  },
  role: {
    "operation.update": "Update Role",
    "operation.create": "Create Role",
    "operation.delete": "Delete Role",
  },
  product_info: {
    title: "Product Information",
    add_product_element: "Add Product Information",
    is_empty: "Is Empty",
    outlet: "Outlet"
  },
  outlet_info: {
    title: "Outlet Information",
    add_outlet_element: "Add Outlet Information",
  },
  data_dict: {},
  device: {
    title: "Device Manager",
    device_name: "Device",
    device_status: "Status:",
    device_title: "Device Name",
    adb_mode: "ADB mode",
    device_detail: "Device Detail",
    device_protocolType: "Device Protocol Type",
    edit_title: "Edit Device",
    add_title: "Add Device to Outlet",
    sn: "Device SN",
    model: "Device Model",
    online_status: "OnLine Status",
    device_number: "Enter Device Serial Number",
    main_device: "Subset Device",
    other_device: "Other Device",
    add: "Add",
    node_test:
      "Note: Sub-device is the camera which can work independently, no need to use algorithm box.",
    bind_device: "Bind Devices to One Principal or One Retail Outlets",
    bind_device_tips: "Please specify the device to a site and zone",
    bind_device_site: "This device will sync the same time zone of the site",

    "operation.create": "Add Device",
    "operation.update": "Update Device",
    "operation.delete": "Delete Device",
    "event.hook.operation.update": "Update Operation event",
    "channel.operation.update": "Update Operation Channel",
    "event.hook.operation.save": "Save Operation event",
    "channel.operation.save": "Save Operation Channel",
    "operation.reboot": "Device Operation Reboot",


  },

  outlets: {
    name: "Outlet Name",
    address: "Outlet Address",
    email: "Email",
    phone: "Mobile",
    main_title: "Retail Main Data",
    please_select_outlet: "Please Select Outlet",
    outlet: "Outlet",
    view_outlet: "View Outlet",
    organization_name: "Organization Name",
    region: "Region",
    organization_owner_email: "Organization Owner Email",
    organization_owner_mobile: "Organization Owner Mobile",
    enter_outlet_name: "Enter Outlet Name",
    outlet_name_required: "Outlet Name is required",
    contracts_mobile: "Contracts Mobile",
    enter_contracts_mobile: "Enter Contracts Mobile",
    mobile_number_required: "Mobile Number is required",
    invalid_mobile_format: "Invalid mobile number format",
    contracts_email: "Contracts Email Address",
    enter_contracts_email: "Enter Contracts Email Address",
    contracts_email_required: "Contracts Email is required",
    invalid_email_format: "Invalid email format",
    outlet_location: "Outlet Location",
    select_an_area: "Select an Area",
    address_required: "Address is required",
    time_zone: "Time Zone",
    select_time_zone: "Select Time Zone",
    time_zone_required: "Time Zone is required",
    enter_address: "Enter Address",
    outlet_list: "Outlet List",
    sure: "Are you sure, you want to delete this outlet permanently?",
    edit_outlet_principal: "Edit Principal Outlet",
    add_outlet_principal: "Add New Outlet",
    add_outlet: "Add Outlet",
    please_enter_outletName: "Please Enter Outlet Name",
    outlets_delete: "This authorization level is using, can not be delete.",

  },

  principal: {
    principal_name: "Principal Name",
    title: "Principal",
    delete_content_on:
      "Some devices and other data are bound to this entity,<br />cannot be deleted.",
    delete_content: "Are you sure you want to permanently delete this entity?",
    add_principal: "Add Principal",
    edit_principal: "Edit Principal",
    principal_name_required: "Please enter Principal name",
    enter_principal_name: "Please enter Principal name",
    principal_owner_email: "Principal Owner Email",
    principal_owner_email_required: "Please enter Principal owner email",
    total_outlets: "Total Outlets",
    enter_total_outlets: "Please enter total outlets",
    mobile_number: "Mobile Number",
    mobile_number_required: "Please enter mobile number",
    outlet_location: "Outlet Location",
    select_an_area: "Select an Area",
    area_required: "Please select an area",
    view_principal: "View Principal",
    organization_name: "Organization Name",
    region: "Region",
    organization_owner_email: "Organization Owner Email",
    organization_owner_mobile: "Organization Owner Mobile",
    address: "Address",


    "operation.update": "Update Principal",
    "operation.create": "Create Principal",
    "operation.delete": "Delete Principal",
  },
  principal_user: {
    title: "Principal Employee",
    view: "Preview",
    logo: "Company Logo",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email Address",
    phone: "Mobile Number",
    password: "Password",
    confirmPassword: "Confirm Password",
    dataScopeId: "Data Permission",
    add_principal: "Add Principal Employee",
    edit_principal: "Edit Principal Employee",
    followAuthorizationLevel: "Follow Authorization Level",
    principal_required: "Principal is required",
    firstName_required: "First name is required",
    lastName_required: "Last name is required",
    email_required: "Email address is required",
    mobile_required: "Mobile number is required",
    password_required: "Password is required",
    confirmPassword_required: "Confirm password is required",
    password_mismatch: "Confirm password does not match the password",
    sure_delete: "Are you sure you want to permanently delete this Principal?",
    delete_no_delete:
      "Some devices and other data are bound to this Principal,<br />cannot be deleted.",
    required_email: "Organization Owner Email is required",
    required_region: "Region is required",
    required_mobile: "Mobile number is required",
    org_name_required: "Organization Name is required"
  },
  partner: {
    title: "Partner",
    logo: "Partner Logo",
    partner_name: "Partner Name",
    enter_partner_name: "Please enter partner name",
    add_partner: "Add Partner",
    edit_partner: "Edit Partner",
    delete_content_on:
      "Some devices and other data are bound to this entity,<br />cannot be deleted.",
    delete_content: "Are you sure you want to permanently delete this entity?",
    email: "Email",
    email_required: "Email is required",
    email_format_error: "Invalid email format",
    mobile_number: "Mobile Number",
    mobile_number_required: "Mobile number is required",
    mobile_format_error: "Invalid mobile number format",
    data_permission: "Data Permission",
    follow_authorization_level: "Follow Authorization Level",
    data_permission_required: "Data permission is required",
    partner_location: "Partner Location",
    enter_partner_location: "Please Enter Partner Location",
    location_required: "Partner location is required",
    password: "Password",
    password_required: "Password is required",
    password_format_error:
      "Must contain uppercase and lowercase letters, numbers, and special characters, and be between 12 and 64 characters in length.",
    confirm_password: "Confirm Password",
    confirm_password_required: "Confirm password is required",
    password_mismatch: "Confirm password does not match the password",
    view: "Preview Partner",

    "operation.create": "Create Partner",
    "operation.delete": "Delete Partner",
    "operation.update": "Update Partner"
  },
  tenant: {
    "operation.update": "Update Tenant",
    "operation.create": "Create Tenant",
    "operation.delete": "Delete Tenant",
  },

  principalEmployee: {
    "operation.update": "Update Principal Employee",
    "operation.delete": "Delete Principal Employee",
    "operation.create": "Create Principal Employee",
  },


  tenantEmployee: {
    "operation.update": "Update Tenant Employee",
    "operation.delete": "Delete Tenant Employee",
    "operation.create": "Create Tenant Employee",
  },
  partner_user: {
    title: "Partner User",
    logo: "Company Logo",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email Address",
    phone: "Mobile Number",
    password: "Password",
    confirmPassword: "Confirm Password",
    dataScopeId: "Responsible Partner",
    followAuthorizationLevel: "Follow Authorization Level",
    principal_required: "Partner is required",
    firstName_required: "First name is required",
    lastName_required: "Last name is required",
    email_required: "Email address is required",
    mobile_required: "Mobile number is required",
    password_required: "Password is required",
    confirmPassword_required: "Confirm password is required",
    password_mismatch: "Confirm password does not match the password",
    sure_delete: "Are you sure you want to permanently delete this partner?",
    delete_no_delete:
      "Some devices and other data are bound to this partner,<br />cannot be deleted.",
    view_title: "View Partner Employee",
    first_name: "First Name",
    last_name: "Last Name",
    region: "Region",
    owner_email: "Organization Owner Email",
    owner_mobile: "Organization Owner Mobile",
    area_name: "Area Name",
    address: "Address",
  },

  login_log: {
    title: "Login Log",
    account: "Login Account",
    user_name: "Login User Name",
    login_time: "Access Time",
    login_ip: "Login IP",
    login_log: "Login Log",
    login_location: "Login Address",
    login_message: "Login Message",
    status: "Status",
  },

  operation_log: {
    name: "Operation Log",
    title: "Operation Method",
    account: "Operation Account",
    ip: "Operation IP",
    address: "Operation Address",
    time: "Operation Time",
  },

  operation: {
    playlist_create: "Create Playlist",
    "upload.file": "Upload File",
    "material.group.create": "Create Material Group",
    "material.group.delete": "Delete Material Group",
    "material.group.update": "Update Material Group",
    "playlist.create": "Create Playlist",
    "playlist.delete": "Delete Playlist",
    "playlist.update": "Update Playlist",
    "total.outlet.create": "Create Total Outlet",
    "total.outlet.delete": "Delete Total Outlet",
    "total.outlet.update": "Update Total Outlet",
    "material.del": "Delete Material",
  },

  template: {
    "operation.update": "Update Message Template",
    "operation.save": "Save Message Template",
    "operation.delete": "Delete Message Template",
  },
  dict: {
    "data.operation.create": "Create Dictionary Data",
    "data.operation.update": "Update Dictionary Data",
    "data.operation.delete": "Delete Dictionary Data",
    "type.operation.create": "Create Dictionary Type",
    "type.operation.update": "Update Dictionary Type",
    "type.operation.delete": "Delete Dictionary Type",
  },

  outlet: {
    "type.operation.create": "Create Outlet Type",
    "type.operation.update": "Update Outlet Type",
    "type.operation.delete": "Delete Outlet Type",
    "operation.create": "Create Outlet",
    "operation.update": "Update Outlet",
    "operation.delete": "Delete Outlet",

  },

  storage: {
    "history.operation.delete": "Delete Operation History",
  },

  people: {
    "counting.operation.export": "Basic People Flow Export",
  },

  event: {
    "matedata.operation.export": "Export Metadata",
  },

  layout: {
    "template.operation.update": "Update Layout Template",
    "template.operation.save": "Save Layout Template",
    "template.operation.delete": "Delete Layout Template",
    "template.operation.create": "Create Layout Template",

  },

  material: {
    "report.operation.export": 'Export Material'
  },


  dataScope: {
    "operation.update": "Update Data Scope",
    "operation.create": "Create Data Scope",
    "operation.delete": "Delete Data Scope",
  },
  product: {
    product: "Product",
    partner_company_name: "Partner Company Name",
    size: "Size",
    bar_code: "Bar Code",
    category_level_1: "Category Level 1",
    category_level_2: "Category Level 2",
    list: "Product List",
    add: "Add Product",
    edit: "Edit Product",
    please_enter: "Please Enter  ",
    "type.operation.create": "Create Product Type",
    "type.operation.update": "Update Product Type",
    "type.operation.delete": "Delete Product Type",
    "operation.update": "Update Product",
    "operation.create": "Create Product",
    "operation.delete": "Delete Product",
    isEmpty_required: "Please Select whether the Template is Empty",
  },

  subscription: {
    get_started: "Get Started",
    trial_package: "Trial",
    basic_package: "Basic Package",
    advanced_package: "Advanced",
    Update: "Update",
    extend: "Extend",
    more: "More Device",
    subscription: "Subscription",
    cmsText: "Screen Direct",
    nutagText: "NuTag",
    zataText: "ZATA",
    starter_package: "Starter",
    professional_package: "Advanced",
    summit_package: "Professional",
    includes: "Includes :",
    free: "Free",
    freeTip: "Free for 2 devices for 15 days",
    getStarted: "Get Started",
    comingSoon: "Coming Soon",
    customerName: "Customer Name",
    enterCustomerName: "Enter Customer Name",
    customerAccount: "Customer Account",
    accountCreatedBy: "Account Created By",
    contractID: "Contract ID",
    enterContractID: "Enter Contract ID",
    contractAmount: "Contract Amount",
    enterContractAmount: "Enter Contract Amount",
    subscriptionPackage: "Subscription Package",
    subscriptionCreationDate: "Subscription Creation Date",
    expirationDate: "Expiration Date",
    activeDate: "Activation Date",
    selectExpirationDate: "Select Expiration Date",
    selectActiveDate: "Select Activation Date",
    numberOfDevice: "Number of Devices",
    enterNumberOfDevice: "Enter Number of Devices",
    accountInformation: "Account Information",
    customerEmailAddress: "Customer Email Address",
    enterCustomerEmailAddress: "Enter Customer Email Address",
    selectCustomer: "Select a Customer",
    area: "Area",
    enterArea: "Please Enter a Area Name",
    mobileNumber: "Mobile Number",
    enterMobileNumber: "Enter Mobile Number",
    contractInformation: "Contract Information",
    amountUnit: "Amount Unit",
    selectAmountUnit: "Select Amount Unit",
    contractFile: "Contract File",
    uploadContract: "Upload Contract",
    uploadCompanyLogo: "Upload Company Logo",
    submit: "Submit",
    emailAddress: "Email Address",
    clickSelectCustomer: "Click on the input box to select the Customer",
    SelectCustomer: "Select a Customer",
    createCustomer: "Create a Customer",
    modifyTitle: "Modify Subscription",
    positive: "Please enter a positive integer",
    tipMsg:
      "The number of devices exceeds the package limit, please delete devices and refresh the page first",
    tipMsg2:
      "The number of devices exceeds the package limit, please contact the administrator",
    tipMsg3:
      "You do not have menu permissions, please contact the administrator",
    payment: "payment",
    paymentMsg: "Are you sure to pay for the current order?",
    singleMaterial: "Single content",
    US$4: "US$4",
    US$10: "US$10",
    US$10: "US$10",
    basic_package_tip: "per device/month(billed annually) US$11 billed monthly",
    areaNameTip:
      "Must start with a letter or Chinese character and can only contain letters, numbers, Chinese characters, spaces, and underscores",
    payStatus: "Pay Status",
    paid: "Paid",
    unpaid: "Unpaid",
    lengthTip: "The length must not exceed {{length}}.",
    minLengthTip: "The length must be greater than {{length}}.",
    moreThen: "The amount cannot be negative.",
    numberMoreThen: "The amount cannot exceed {{number}}.",
    singleFile: "Only a single file is allowed for upload.",
    dropTip: "Drag and drop here to upload.",
    limtFileSize: "The file size cannot exceed {{tip}}.",
    Account_Information: "Account Information",
    contract_information: "Contract Information",
    enter_Dev_number: "Please enter the number of devices",
    clinet_name: "Customer Client Name",
    please_enter_clinet_name: "Please enter Client Name",
    customer_email_address: "Customer Email Address",
    please_enter_email_address: "Please Enter Customer Email Address",
    email_formatted: "The email is not formatted correctly",
    enter_customer_code: "PLease Enter Customer Code",
    client_Code: "Client Code",
    please_select_location: "Please select location.",
    please_enter_first_name: "Please Enter First Name",
    please_enter_last_name: "Please Enter Last Name",
    please_select_country: "Please Select Country",
    please_select_province: "Please Select Province ",
    please_select_city: "Please Select City",
    please_formatted_phone: "Please enter the correct mobile phone number",
    only_number: "Only numbers may be entered",
    Monthly: "Monthly",
    Annually: "Annually",
    extend_months: "Extend Months Of Subscribe",
    extend_years: "Extend Years Of Subscribe",
    subMonths: "Months Of Subscribe",
    subAnnually: "Years Of Subscribe",
    please_enter_number_months: "Please Enter Number of Months",
    please_enter_number_Annually: "Please Enter Number of Years",
    please_subscription_number:
      "Please enter the monthly/annual number of subscriptions",
    please_select_zoonTime: "Please select time zone",
    please_select_startTime: "Please select a start time",
    please_select_endTime: "Please select a end time",
    Contract_ID: "Contract ID",
    please_enter_contract_Id: "Please Enter Contract ID",
    please_enter_contract_amount: "Please Enter Contract Amount",
    please_enter_contract_unit: "Please Select Contract Unit",
    Contract_Amount: "Contract Amount",
    Contract_Unit: "Contract Unit",
    Device_Count: "Device Count",
    subCreatedAt: "subCreatedAt",
    select_retail_clinet: "Please select a Customer",
    create_principal: "Create Principal",
    select_principal: "Select Principal",
    subsctiption_content: "SUBSCRIPTION",
    account_information: "ACCOUNT INFORMATION",
    add_subscription: "Add Subscription",
    update_subscription: "Upgrade Package",
    add_device: "Add Device",
    extend_subscription: "Extend Subscription",
    unknown_type: "Unknown Subscription",
    trial_package: "Trial",
    basic_package: "Basic Package",
    advanced_package: "Advanced",
    Update: "Update",
    extend: "Extend",
    more: "More Devices",
    subscription: "Subscription",
    principal_region: "Principal Region",
    enter_region_name: "Please Enter Region Name",
    enter_years_time: "Please Enter",

    "operation.create": "Create Subscription"
  },
  login: {
    email: "Email",
    mobile: "Mobile",
    password: "Password",
    remember_me: "Remember me",
    forgot_password: "Forgot Password?",
    login_button: "Login",
    verification_code: "Verification Code",
    enter_email: "Enter your email",
    enter_password: "Enter your password",
    enter_verification_code: "Enter verification code",
    user_agreement: "I have read and agree with",
    user_agreement_link: "User Agreement",
    privacy_policy_link: "Privacy Policy",
    and: "and"
  },
};
export default pages;
