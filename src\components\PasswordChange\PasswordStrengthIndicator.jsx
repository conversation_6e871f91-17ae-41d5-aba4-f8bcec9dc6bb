import React from "react";
import { Box, Typography, LinearProgress, Chip, Stack } from "@mui/material";
import { Check, Close } from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import RegexCode from "@/enums/RegexCode";

const StyledLinearProgress = styled(LinearProgress)(({ theme, strength }) => ({
  height: 6,
  borderRadius: 3,
  backgroundColor: theme.palette.grey[200],
  "& .MuiLinearProgress-bar": {
    borderRadius: 3,
    backgroundColor:
      strength === "weak"
        ? theme.palette.error.main
        : strength === "medium"
        ? theme.palette.warning.main
        : strength === "strong"
        ? theme.palette.success.main
        : theme.palette.grey[300],
  },
}));

const RequirementChip = styled(Chip)(({ theme, met }) => ({
  fontSize: "0.75rem",
  height: 24,
  backgroundColor: met ? theme.palette.success.light : theme.palette.grey[100],
  color: met
    ? theme.palette.success.contrastText
    : theme.palette.text.secondary,
  "& .MuiChip-icon": {
    fontSize: "0.875rem",
  },
}));

// 密码强度计算函数
const calculatePasswordStrength = (password) => {
  if (!password) return { score: 0, strength: "none" };

  let score = 0;
  const requirements = {
    length: password.length >= 12, // 更新为12位最小长度
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[^\w\s]/.test(password), // 使用与正则表达式一致的特殊字符检测
  };

  // 基础分数
  if (requirements.length) score += 20;
  if (requirements.lowercase) score += 15;
  if (requirements.uppercase) score += 15;
  if (requirements.number) score += 15;
  if (requirements.special) score += 15;

  // 长度奖励
  if (password.length >= 12) score += 10;
  if (password.length >= 16) score += 10;

  // 确定强度等级
  let strength = "weak";
  if (score >= 80) strength = "strong";
  else if (score >= 60) strength = "medium";

  return { score, strength, requirements };
};

const PasswordStrengthIndicator = ({
  password = "",
  showRequirements = true,
  showScore = false,
}) => {
  const { score, strength, requirements } = calculatePasswordStrength(password);

  const strengthLabels = {
    none: "请输入密码",
    weak: "弱",
    medium: "中等",
    strong: "强",
  };

  const strengthColors = {
    none: "default",
    weak: "error",
    medium: "warning",
    strong: "success",
  };

  const requirementsList = [
    { key: "length", label: "至少12位字符", met: requirements?.length },
    { key: "lowercase", label: "包含小写字母", met: requirements?.lowercase },
    { key: "uppercase", label: "包含大写字母", met: requirements?.uppercase },
    { key: "number", label: "包含数字", met: requirements?.number },
    { key: "special", label: "包含特殊字符", met: requirements?.special },
  ];

  if (!password && !showRequirements) return null;

  return (
    <Box sx={{ mt: 1 }}>
      {/* 密码强度指示器 */}
      <Box sx={{ mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 1,
          }}>
          <Typography variant="body2" color="text.secondary">
            密码强度
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            {showScore && (
              <Typography variant="body2" color="text.secondary">
                {score}/100
              </Typography>
            )}
            {/* <Chip
              label={strengthLabels[strength]}
              size="small"
              color={strengthColors[strength]}
              variant={strength === "none" ? "outlined" : "filled"}
            /> */}
          </Box>
        </Box>

        <StyledLinearProgress
          variant="determinate"
          value={score}
          strength={strength}
        />
      </Box>

      {/* 密码要求列表 */}
      {showRequirements && password && (
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            密码要求：
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {requirementsList.map((req) => (
              <RequirementChip
                key={req.key}
                label={req.label}
                size="small"
                met={req.met}
                icon={req.met ? <Check /> : <Close />}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Box>
  );
};

// 密码验证工具函数
export const validatePassword = (password, currentPassword = "") => {
  const errors = [];

  if (!password) {
    errors.push("请输入密码");
    return errors;
  }

  // 使用统一的正则表达式进行验证
  if (!RegexCode.PASSWORD_REGEX.test(password)) {
    if (password.length < 12) {
      errors.push("密码长度至少12位");
    } else if (password.length > 64) {
      errors.push("密码长度不能超过64位");
    } else if (!/[a-z]/.test(password)) {
      errors.push("密码必须包含小写字母");
    } else if (!/[A-Z]/.test(password)) {
      errors.push("密码必须包含大写字母");
    } else if (!/\d/.test(password)) {
      errors.push("密码必须包含数字");
    } else if (!/[^\w\s]/.test(password)) {
      errors.push("密码必须包含特殊字符");
    } else if (
      /[\s\u{1F300}-\u{1F9FF}\u{2600}-\u{27BF}\u{1F1E6}-\u{1F1FF}]/u.test(
        password
      )
    ) {
      errors.push("密码不能包含空格或表情符号");
    } else {
      errors.push(t("common.common_confirm_password_format"));
    }
  }

  if (currentPassword && password === currentPassword) {
    errors.push("新密码不能与当前密码相同");
  }

  return errors;
};

// 检查密码强度是否足够
export const isPasswordStrong = (password) => {
  const { strength } = calculatePasswordStrength(password);
  return strength === "strong" || strength === "medium";
};

export default PasswordStrengthIndicator;
