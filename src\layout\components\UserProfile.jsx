import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { useNavigate } from "react-router-dom";
import {
  bindPopover,
  bindTrigger,
  usePopupState,
} from "material-ui-popup-state/hooks";
import React, { useState } from "react";
import { pxToRem } from "@/utils/zkUtils.js";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import UserIcon from "@/assets/menuIcon/User.svg?react";
import AboutIcon from "@/assets/menuIcon/About.svg?react";
import OutLogin from "@/assets/menuIcon/OutLogin.svg?react";
import MyScriptionion from "@/assets/menuIcon/MySubScription.svg?react";
import { useTheme } from "@mui/material/styles";
import { useStateUserInfo } from "@/hooks/user.js";
import { removeToken } from "@/utils/auth";
import { clearUser } from "@/store/reducers/user";
import About from "./About.jsx";
import { logout } from "@/service/api/user.js";
import SvgIcon from "@/components/SvgIcon";
import { toast } from "react-toastify";
function UserProfile(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });

  const navigate = useNavigate();
  const resData = useStateUserInfo();
  const [open, setOpen] = useState(false);
  const [closeOpen, setCloseOpen] = useState(false);
  const [selectStatus, setSelectStatus] = useState(0); // 是否选中

  return (
    <div>
      <Box
        className={
          "flex items-center justify-between h-[40px] px-4 py-1 hover:bg-gray-100 focus:bg-gray-100 rounded-md cursor-pointer"
        }
        display="flex"
        alignItems={"center"}
        justifyContent={"center"}
        // style={{ cursor: "pointer", marginTop: "3px" }}
        {...bindTrigger(popupState)}>
        <div className="flex gap-2 items-center">
          <Avatar sx={{ width: "32px", height: "32px" }} src={resData?.photo} />
          {props.open && (
            <Tooltip title={"ZKDIGIMAX"} arrow placement="bottom">
              <div
                className="text-gray-400 text-[14px]"
                // sx={{
                //   color: "rgba(71, 75, 79, 0.6)",
                //   font: `normal normal normal 14px/18px Proxima Nova`,
                // }}
              >
                {resData?.firstName}
                {/* {resData?.firstName} {resData?.lastName} */}
              </div>
            </Tooltip>
          )}
        </div>

        {props.open && (
          <Box>
            <div variant="menuItem">
              {popupState.isOpen ? (
                <KeyboardArrowLeftIcon
                  fontSize="small"
                  style={{
                    color: `#A2A3A3`,
                  }}
                />
              ) : (
                <KeyboardArrowRightIcon
                  fontSize="small"
                  style={{
                    color: `#A2A3A3`,
                  }}
                />
              )}
            </div>
          </Box>
        )}
      </Box>
      <Popover
        {...bindPopover(popupState)}
        // sx={{marginBottom:10}}
        sx={{
          "& .MuiPaper-root": {
            marginBottom: 10,
            width: "250px",
          },
        }}
        // className="w-[350px]"
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}>
        <div className="my-1 mx-1">
          <div className="flex  flex-row items-center px-2 gap-2 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-md">
            <Avatar sx={{ width: 32, height: 32 }} src={resData?.photo} />
            <div>
              <Tooltip title={"ZKDIGIMAX"} arrow placement="bottom">
                <div className="text-[14px] font-[500]">
                  {resData?.firstName} {resData?.lastName}
                </div>
              </Tooltip>

              <Tooltip title={resData?.email} arrow placement="bottom">
                <div className="text-[14px] font-[400] text-gray-400">
                  {resData?.email}
                </div>
              </Tooltip>
            </div>
          </div>
          <Divider className="my-1" />
          <div className="flex flex-col">
            <TabButtom
              Icon={
                <SvgIcon
                  icon="iconamoon:profile-thin"
                  height="1.5em"
                  className="text-gray-400"
                />
              }
              Text={t("common.my_profile")}
              selectStatus={selectStatus === 0}
              onClick={() => {
                popupState.close();
                setSelectStatus(0);
                navigate("/menuItem/userprofile");
              }}></TabButtom>

            <TabButtom
              Icon={
                <SvgIcon
                  icon="iconamoon:credit-card-thin"
                  height="1.5em"
                  className="text-gray-400"
                />
              }
              Text={t("common.my_subscription")}
              selectStatus={selectStatus === 2}
              onClick={() => {
                setSelectStatus(2);
                navigate("/menuItem/mySubsctiption");
                popupState.close();
              }}></TabButtom>
            <TabButtom
              Icon={
                <SvgIcon
                  icon="iconamoon:information-circle-thin"
                  height="1.5em"
                  className="text-gray-400"
                />
              }
              // Icon={<AboutIcon></AboutIcon>}
              Text={t("common.about")}
              selectStatus={selectStatus === 1}
              onClick={() => {
                setSelectStatus(1);
                setOpen(true);
                popupState.close();
              }}></TabButtom>

            <TabButtom
              Icon={
                <SvgIcon
                  icon="iconamoon:information-circle-thin"
                  height="1.5em"
                  className="text-gray-400"
                />
              }
              // Icon={<AboutIcon></AboutIcon>}
              Text={t("Close Account")}
              selectStatus={selectStatus === 4}
              onClick={() => {
                setSelectStatus(4);
                setCloseOpen(true);
                popupState.close();
              }}></TabButtom>

            <TabButtom
              Icon={
                <SvgIcon
                  icon="iconamoon:exit-thin"
                  className="text-red-600"
                  height="1.5em"
                />
              }
              Text={t("common.logout")}
              theme="danger"
              selectStatus={selectStatus === 3}
              onClick={async () => {
                setSelectStatus(3);

                popupState.close();
                dispatch(clearUser());
                // 清除其他可能的session存储
                // 选择性清除 localStorage 中的数据，保留记住我功能相关数据
                const rememberMe = localStorage.getItem("rememberMe");
                const username = localStorage.getItem("username");
                const password = localStorage.getItem("password");

                // 先保存记住我相关数据
                const rememberMeData = {
                  rememberMe,
                  username,
                  password,
                };

                // 清除所有 localStorage 数据
                localStorage.clear();

                // 如果之前启用了记住我功能，则恢复相关数据
                if (rememberMeData.rememberMe === "true") {
                  localStorage.setItem("rememberMe", rememberMeData.rememberMe);
                  localStorage.setItem("username", rememberMeData.username);
                  localStorage.setItem("password", rememberMeData.password);
                }

                // 清除 sessionStorage 中的所有数据
                sessionStorage.clear();
                // 清除所有 cookie
                document.cookie.split(";").forEach(function (c) {
                  document.cookie = c
                    .replace(/^ +/, "")
                    .replace(
                      /=.*/,
                      "=;expires=" + new Date().toUTCString() + ";path=/"
                    );
                });
                const res = await logout();
                toast.success(res?.message);
                // 移除token
                removeToken();

                navigate("/login");
              }}></TabButtom>
            {/* <Grid
              container
              spacing={1}
              sx={{
                display: "flex",
                justifyContent: "center",
                flexDirection: "column",
                mt: 1,
              }}
            >
              <Grid item>
                <TabButtom
                  Icon={<UserIcon></UserIcon>}
                  Text={"My Profile"}
                  selectStatus={selectStatus === 0}
                  onClick={() => {
                    popupState.close();
                    setSelectStatus(0);
                    navigate("/menuItem/userprofile");
                  }}
                ></TabButtom>
              </Grid>
              <Grid item>
                <TabButtom
                  Icon={<AboutIcon></AboutIcon>}
                  Text={"About"}
                  selectStatus={selectStatus === 1}
                  onClick={() => {
                    setSelectStatus(1);
                    setOpen(true);
                    popupState.close();
                  }}
                ></TabButtom>
              </Grid>
              <Grid item>
                <TabButtom
                  Icon={<MyScriptionion></MyScriptionion>}
                  Text={"My Subscription"}
                  selectStatus={selectStatus === 2}
                  onClick={() => {
                    setSelectStatus(2);
                    popupState.close();
                  }}
                ></TabButtom>
              </Grid>
              <Grid item>
                <TabButtom
                  Icon={<OutLogin></OutLogin>}
                  Text={"Logout"}
                  selectStatus={selectStatus === 3}
                  onClick={async () => {
                    setSelectStatus(3);

                    popupState.close();
                    dispatch(clearUser());
                    // 清除其他可能的session存储
                    localStorage.removeItem("tenantCode");
                    localStorage.removeItem("departmentId");
                    localStorage.removeItem("templateJson");

                    sessionStorage.removeItem("tenantCode");
                    sessionStorage.removeItem("loginRoleKey");
                    const res = await logout();
                    toast.success(res?.message);
                    // 移除token
                    removeToken();

                    navigate("/login");
                  }}
                ></TabButtom>
              </Grid>
            </Grid> */}
          </div>
        </div>
      </Popover>
      <About open={open} setOpen={setOpen}></About>
    </div>
  );
}
export default UserProfile;

const TabButtom = (props) => {
  const { Icon, Text, onClick, theme } = props;
  return (
    <div
      className="flex text-[14px] flex-row items-center px-2 py-2 gap-2 hover:bg-gray-100 focus:bg-gray-100 rounded-md"
      onClick={onClick}>
      <div>{Icon}</div>
      <div
        className={
          theme && theme === "danger" ? "text-red-600" : "text-gray-500"
        }>
        {Text}
      </div>
    </div>
  );
};
