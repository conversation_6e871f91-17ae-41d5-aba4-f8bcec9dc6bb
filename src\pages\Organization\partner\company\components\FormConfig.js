
import RegexCode from "@/enums/RegexCode";

export const getFormConfig = (t, type, formik) => {
  let formConfig = [
    {
      name: "name",
      label: t("partner.partner_name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.enter_partner_name"),
        },
      ],
    },
    {
      name: "email",
      label: t("partner.email"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.email_required"),
        },
        {
          type: "email",
          message: t("partner.email_format_error"),
        },
      ],
    },

    {
      codename: "countryCode",
      name: "phone",
      label: t("partner.mobile_number"),
      type: "mobile",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.mobile_number_required"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("partner.mobile_format_error"),
        },
      ],
    },

    {
      name: "areaName",
      label: t("partner.partner_location"),
      type: "input",
      required: true,
      placeholder: t("partner.enter_partner_location"),
      // data: treeList,
      // onChange: (item) => {
      //   formik.setFieldValue("areaName", item.name);
      //   formik.setFieldValue("areaId", item.id);
      //   const [longitude, latitude] = item.location.split(",");
      //   formik.setFieldValue("longitude", parseFloat(longitude));
      //   formik.setFieldValue("latitude", parseFloat(latitude));
      // },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.location_required"),
        },
      ],
    },

    {
      name: "password",
      label: t("partner.password"),
      type: "password",
      viewPwd: true,
      required: true,
      display: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("partner.password_required"),
            },
            {
              type: "matches",
              matches: RegexCode.PASSWORD_REGEX,
              message: t("partner.password_format_error"),
            },
          ]
          : null,
    },

    {
      name: "confirmPassword",
      label: t("partner.confirm_password"),
      type: "password",
      viewPwd: true,
      display: type !== "editor" ? true : false,
      required: true,
      validation:
        type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("partner.confirm_password_required"),
            },
            {
              type: "secondConfirm",
              ref: "password",
              message: t("partner.password_mismatch"),
            },
          ]
          : null,
    },
  ];

  return formConfig;
};
